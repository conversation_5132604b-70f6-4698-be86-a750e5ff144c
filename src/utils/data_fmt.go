package utils

import (
	"encoding/json"
	"tcloud/wolong/utils/plog"
)

func RemoveJSONField(jsonStr string, fieldToRemove string) (string, error) {
	var data map[string]json.RawMessage
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", err
	}

	delete(data, fieldToRemove)

	result, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	return string(result), nil
}

// MergeMap 合并map数据
func MergeMap(origin map[int]interface{}, addMap map[int]interface{}, isConst bool) {

	for id, value := range addMap {
		if origin[id] != nil && !isConst {
			plog.Error("ID冲突 id=", id)
		}
		origin[id] = value
	}
}
