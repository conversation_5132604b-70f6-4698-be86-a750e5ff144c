package main

import (
	"flag"
	"tcloud/wolong/conf"
	"tcloud/wolong/deploy"
	"tcloud/wolong/excel"
	"tcloud/wolong/utils/plog"
)

var (
	confFile       = flag.String("config", "config.toml", "-config config.toml")
	systemConfFile = flag.String("config_system", "config_system.toml", "-config_system config_system.toml")
	output         = flag.String("output", "data", "output directory")
	stub           = flag.String("stub", "stub", "generated stub code files directory")
	locale         = flag.String("locale", "locale", "generated locale files directory")
	exportJsonCfg  = flag.Bool("json", false, "export config.toml in json datatype")
	golang         = flag.Bool("go", false, "generate golang stub code")
	csharp         = flag.Bool("cs", false, "generate csharp stub code")
	snake          = flag.Bool("snake", false, "use snake case for generated code")
	client         = flag.Bool("client", true, "generate client code")
	consul         = flag.Bool("consul", false, "publish to consul")
	ftp            = flag.Bool("ftp", false, "ftp to oss")
	plt            = flag.Bool("plt", false, "upload to platform")
	debug          = flag.Bool("debug", false, "debug mode")
)

// 参数解析
func main() {
	// 解析命令行参数
	flag.Parse()

	// 初始化日志文件
	plog.InitLog("wolong.log", plog.LogTrace)

	// 加载配置Toml文件
	conf.Init(*confFile, *systemConfFile)

	// 初始化Consul
	if *consul {
		deploy.InitConsul()
	}

	if *plt {
		deploy.InitMysqlCfg()
	}

	// 过滤生成目标
	conf.Filter(*client)
	if exportJsonCfg != nil && *exportJsonCfg {
		// 在根目录将配置内容导出到json文件
		conf.ExportJson("config.json")
	}

	// 启动解析器
	parser := excel.NewParser(*output, *stub, *locale, *golang, *csharp, *snake, *consul, *ftp, *plt, *debug)
	parser.Start()
}
