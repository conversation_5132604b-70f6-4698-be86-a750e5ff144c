// Package plog
// * 自己实现log库而不使用标准log库的原因是我们对log有一些自己的定制化需求,比如：
// * 1. log分级
// * 2. 每日log需写入到一个文件，每月log需要归拢到一个文件夹
// * 3. 根据运维要求进行小幅修改
// * 系统的log太过简单。而第三方库引入未知复杂度且不一定能解决问题
// * 因此，根据需求实现一个简单版本,底层仍然使用标准log库
package plog

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"time"

	"github.com/fatih/color"
)

var logLevel LogLevel = LogInfo

type LogLevel int

const (
	LogTrace LogLevel = 0
	LogDebug LogLevel = 1
	LogInfo  LogLevel = 2
	LogWarn  LogLevel = 3
	LogError LogLevel = 4
	LogCrit  LogLevel = 5
	LogPanic LogLevel = 6
	LogFatal LogLevel = 7
)

func InitLog(logFile string, level LogLevel) {
	logLevel = level
	file, err := os.OpenFile(logFile, os.O_RDWR|os.O_TRUNC|os.O_CREATE, 0666)
	if err != nil {
		Fatal("Can't open log file")
	}
	log.SetOutput(file)
}

func Trace(v ...interface{}) {
	if logLevel > LogTrace {
		return
	}

	var prefix string
	timeNow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [Trace]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}

	color.Set(color.FgBlue)
	fmt.Printf("%s %s ", timeNow, prefix)
	fmt.Println(v...)
	color.Unset()
	log.Println(append([]interface{}{timeNow, prefix}, v...)...)
}

func Tracef(format string, v ...interface{}) {
	if logLevel > LogTrace {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [Trace]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgBlue)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Printf(format, v...)
	color.Unset()
	log.Println(append([]interface{}{timenow, prefix}, v...)...)
}

func Debug(v ...interface{}) {
	if logLevel > LogDebug {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [DEBUG]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgCyan)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Println(v...)
	color.Unset()
	log.Println(append([]interface{}{timenow, prefix}, v...)...)
}

func Debugf(format string, v ...interface{}) {
	if logLevel > LogDebug {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [DEBUG]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgCyan)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Printf(format, v...)
	color.Unset()
	log.Printf("%s %s"+format, append([]interface{}{timenow, prefix}, v...)...)
}

func Info(v ...interface{}) {
	if logLevel > LogInfo {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [INFO]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgYellow)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Println(v...)
	color.Unset()
	log.Println(append([]interface{}{timenow, prefix}, v...)...)
}

func Infof(format string, v ...interface{}) {
	if logLevel > LogInfo {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [INFO]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgYellow)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Printf(format, v...)
	color.Unset()
	log.Printf("%s %s"+format, append([]interface{}{timenow, prefix}, v...)...)
}

func Warn(v ...interface{}) {
	if logLevel > LogWarn {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [WARN]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgRed)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Println(v...)
	color.Unset()
	log.Println(append([]interface{}{timenow, prefix}, v...)...)
}

func WarnF(format string, v ...interface{}) {
	if logLevel > LogWarn {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [WARN]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgRed)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Printf(format, v...)
	color.Unset()
	log.Printf("%s %s"+format, append([]interface{}{timenow, prefix}, v...)...)
}

func Error(v ...interface{}) {
	if logLevel > LogError {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [ERROR]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgRed)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Println(v...)
	color.Unset()
	log.Println(append([]interface{}{timenow, prefix}, v...)...)
}

func Errorf(format string, v ...interface{}) {
	if logLevel > LogError {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [ERROR]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgRed)
	fmt.Printf("%s %s ", timenow, prefix)
	fmt.Printf(format, v...)
	color.Unset()
	log.Printf("%s %s"+format, append([]interface{}{timenow, prefix}, v...)...)
	os.Exit(1)
}

func Critical(v ...interface{}) {
	if logLevel > LogCrit {
		return
	}

	var prefix string
	timenow := time.Now().Format("15:04:05")
	if funcPtr, filePath, line, ok := runtime.Caller(1); ok {
		prefix = fmt.Sprintf("%s:%d %s -> [CRIT]", filepath.Base(filePath), line, runtime.FuncForPC(funcPtr).Name())
	}
	color.Set(color.FgRed)
	fmt.Println(append([]interface{}{timenow, prefix}, v...)...)
	color.Unset()
	log.Println(append([]interface{}{timenow, prefix}, v...)...)
}

func Fatal(v ...interface{}) {
	if logLevel > LogFatal {
		return
	}

	if funcPtr, file, line, ok := runtime.Caller(1); ok {
		log.Print("  "+runtime.FuncForPC(funcPtr).Name(), " ", file, " ", line, "\n")
	}
	log.Fatal(v...)
}

func Panic(v ...interface{}) {
	if logLevel > LogPanic {
		return
	}

	log.Panic(v...)
}

func ShowError(v ...interface{}) {
	log.Panic(v...)
}

func ShowErrorF(fmt string, v ...interface{}) {
	log.Panicf(fmt, v...)
}
